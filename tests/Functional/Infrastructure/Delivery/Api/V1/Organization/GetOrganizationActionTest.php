<?php

declare(strict_types=1);

namespace App\Tests\Functional\Infrastructure\Delivery\Api\V1\Organization;

use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Shared\Factory\OrganizationFactory;

final class GetOrganizationActionTest extends FunctionalTestCase
{
    protected const string ROUTE = '/tenants/v1/{_locale}/organizations/{id}';
    protected const string METHOD = 'GET';
    protected const string LOCALE = 'en';

    public function testGetOrganizationWorks(): void
    {
        // First create an organization to get
        $createData = [
            'name' => 'Organization to Get',
            'email' => '<EMAIL>',
            'phone' => '******-0126',
            'address' => '126 Get Street'
        ];
        
        $createRoute = str_replace('{_locale}', self::LOCALE, '/tenants/v1/{_locale}/organizations');
        $this->client->jsonRequest('POST', $createRoute, $createData);
        $this->assertResponseStatusCodeSame(201);
        $createResponse = $this->getDecodedJsonResponse();
        $organizationId = $createResponse['data']['id'];

        // Now get the organization
        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $organizationId], self::ROUTE);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(200);

        $response = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $response);
        $this->assertEquals($organizationId, $response['data']['id']);
        $this->assertEquals($createData['name'], $response['data']['name']);
        $this->assertEquals($createData['email'], $response['data']['email']);
        $this->assertEquals($createData['phone'], $response['data']['phone']);
        $this->assertEquals($createData['address'], $response['data']['address']);
        $this->assertNull($response['data']['deletedAt']);
        $this->assertNotNull($response['data']['createdAt']);
        $this->assertNotNull($response['data']['updatedAt']);
    }

    public function testGetOrganizationFailsWithNotFound(): void
    {
        $nonExistentId = '550e8400-e29b-41d4-a716-446655440000';
        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $nonExistentId], self::ROUTE);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(404);
    }

    public function testGetExistingOrganizationFromFixtures(): void
    {
        // Get the organization that exists in fixtures
        // We need to find its ID first by listing organizations
        $listRoute = str_replace('{_locale}', self::LOCALE, '/tenants/v1/{_locale}/organizations');
        $this->client->request('GET', $listRoute);
        $this->assertResponseStatusCodeSame(200);
        
        $listResponse = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $listResponse);
        $this->assertArrayHasKey('items', $listResponse['data']);
        $this->assertNotEmpty($listResponse['data']['items']);

        // Find the organization with the name from fixtures
        $fixtureOrganization = null;
        foreach ($listResponse['data']['items'] as $org) {
            if ($org['name'] === OrganizationFactory::NAME) {
                $fixtureOrganization = $org;
                break;
            }
        }
        
        $this->assertNotNull($fixtureOrganization, 'Fixture organization not found');
        
        // Now get this specific organization
        $route = str_replace(['{_locale}', '{id}'], [self::LOCALE, $fixtureOrganization['id']], self::ROUTE);
        $this->client->request(self::METHOD, $route);
        $this->assertResponseStatusCodeSame(200);

        $response = $this->getDecodedJsonResponse();
        $this->assertArrayHasKey('data', $response);
        $this->assertEquals($fixtureOrganization['id'], $response['data']['id']);
        $this->assertEquals(OrganizationFactory::NAME, $response['data']['name']);
        $this->assertEquals(OrganizationFactory::EMAIL, $response['data']['email']);
        $this->assertEquals(OrganizationFactory::PHONE, $response['data']['phone']);
        $this->assertEquals(OrganizationFactory::ADDRESS, $response['data']['address']);
    }
}
